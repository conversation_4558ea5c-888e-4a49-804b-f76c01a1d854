# Recreate the full beat again with ambient pad, sub-melody, bass, kick, and snare
from pydub.generators import <PERSON><PERSON>
from pydub import AudioSegment

# Main ambient tone (pad)
melody = Sine(440).to_audio_segment(duration=10000).fade_in(2000).fade_out(2000).low_pass_filter(300)
sub_melody = Sine(60).to_audio_segment(duration=10000).fade_in(1500).fade_out(1500).low_pass_filter(100)
pad = melody.overlay(sub_melody - 10)

# Add deep bass layer
bass = Sine(45).to_audio_segment(duration=10000).apply_gain(-6).low_pass_filter(80)
pad_with_bass = pad.overlay(bass)

# Drums setup (kick + snare)
kick = Sine(100).to_audio_segment(duration=180).apply_gain(+4).low_pass_filter(120)
snare = Sine(800).to_audio_segment(duration=120).apply_gain(-2).high_pass_filter(400)

# Drum pattern on 4/4
drum_track = AudioSegment.silent(duration=10000)
for i in range(0, 10000, 2000):
    drum_track = drum_track.overlay(kick, position=i)
    drum_track = drum_track.overlay(snare, position=i + 1000)

# Final mix: ambient + bass + drums
full_beat = pad_with_bass.overlay(drum_track - 3)

# Export beat
final_audio_path = "/mnt/data/drake_autotune_style_fullbeat.mp3"
full_beat.export(final_audio_path, format="mp3")
final_audio_path