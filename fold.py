# Simple beat generator using basic Python libraries
import math
import wave
import struct

def generate_sine_wave(frequency, duration, sample_rate=44100, amplitude=0.5):
    """Generate a sine wave"""
    frames = int(duration * sample_rate)
    wave_data = []
    for i in range(frames):
        value = amplitude * math.sin(2 * math.pi * frequency * i / sample_rate)
        wave_data.append(value)
    return wave_data

def apply_fade(wave_data, fade_in_duration=0.5, fade_out_duration=0.5, sample_rate=44100):
    """Apply fade in and fade out to wave data"""
    fade_in_frames = int(fade_in_duration * sample_rate)
    fade_out_frames = int(fade_out_duration * sample_rate)

    # Fade in
    for i in range(min(fade_in_frames, len(wave_data))):
        wave_data[i] *= i / fade_in_frames

    # Fade out
    for i in range(min(fade_out_frames, len(wave_data))):
        idx = len(wave_data) - 1 - i
        wave_data[idx] *= i / fade_out_frames

    return wave_data

def mix_waves(wave1, wave2, volume1=1.0, volume2=1.0):
    """Mix two wave arrays together"""
    max_len = max(len(wave1), len(wave2))
    mixed = [0.0] * max_len

    for i in range(max_len):
        val1 = wave1[i] * volume1 if i < len(wave1) else 0
        val2 = wave2[i] * volume2 if i < len(wave2) else 0
        mixed[i] = val1 + val2

    return mixed

def save_wave_file(wave_data, filename, sample_rate=44100):
    """Save wave data to a WAV file"""
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 2 bytes per sample
        wav_file.setframerate(sample_rate)

        # Convert to 16-bit integers
        for sample in wave_data:
            # Clamp to prevent clipping
            sample = max(-1.0, min(1.0, sample))
            wav_file.writeframes(struct.pack('<h', int(sample * 32767)))

# Generate the beat components
print("Generating ambient pad...")
melody = generate_sine_wave(440, 10, amplitude=0.3)  # 10 seconds
melody = apply_fade(melody, 2, 2)  # 2 second fade in/out

sub_melody = generate_sine_wave(60, 10, amplitude=0.2)
sub_melody = apply_fade(sub_melody, 1.5, 1.5)

# Mix melody and sub-melody
pad = mix_waves(melody, sub_melody, 1.0, 0.8)

print("Adding bass layer...")
bass = generate_sine_wave(45, 10, amplitude=0.4)
pad_with_bass = mix_waves(pad, bass, 1.0, 0.6)

print("Creating drum pattern...")
# Create kick and snare sounds
kick = generate_sine_wave(100, 0.18, amplitude=0.8)  # 180ms
snare = generate_sine_wave(800, 0.12, amplitude=0.4)  # 120ms

# Create drum track (10 seconds)
drum_track = [0.0] * (44100 * 10)  # 10 seconds of silence

# Add drums every 2 seconds (kick) and 1 second after kick (snare)
for beat in range(5):  # 5 beats in 10 seconds
    kick_start = beat * 2 * 44100  # Every 2 seconds
    snare_start = kick_start + 44100  # 1 second after kick

    # Add kick
    for i, sample in enumerate(kick):
        if kick_start + i < len(drum_track):
            drum_track[kick_start + i] += sample

    # Add snare
    for i, sample in enumerate(snare):
        if snare_start + i < len(drum_track):
            drum_track[snare_start + i] += sample

print("Mixing final beat...")
# Mix everything together
full_beat = mix_waves(pad_with_bass, drum_track, 1.0, 0.7)

# Save the final beat
output_file = "drake_style_beat.wav"
print(f"Saving beat to {output_file}...")
save_wave_file(full_beat, output_file)

print(f"Beat successfully exported to: {output_file}")
print("You can find the file in your current directory!")