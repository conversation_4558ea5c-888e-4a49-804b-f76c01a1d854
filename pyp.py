from pydub import AudioSegment
from pydub.generators import Sine
from gtts import gTTS
import numpy as np
import scipy.signal as sps

# 1. توليد بيت موسيقي حوالي 3 دقايق
duration_ms = 3 * 60 * 1000  # 3 دقائق

# Pad ambient
melody = Sine(440).to_audio_segment(duration=duration_ms).fade_in(2000).fade_out(2000).low_pass_filter(300)
sub_melody = Sine(60).to_audio_segment(duration=duration_ms).fade_in(1500).fade_out(1500).low_pass_filter(100)
pad = melody.overlay(sub_melody - 10)

# Bass عميق
bass = Sine(45).to_audio_segment(duration=duration_ms).apply_gain(-6).low_pass_filter(80)
pad_with_bass = pad.overlay(bass)

# Drums 4/4
kick = Sine(100).to_audio_segment(duration=200).apply_gain(+4).low_pass_filter(120)
snare = Sine(800).to_audio_segment(duration=150).apply_gain(-2).high_pass_filter(400)
drums = AudioSegment.silent(duration=duration_ms)
for i in range(0, duration_ms, 2000):
    drums = drums.overlay(kick, position=i)
    drums = drums.overlay(snare, position=i + 1000)

beat = pad_with_bass.overlay(drums - 3)

# 2. إعداد الكلمات
lyrics = """
It’s 2 a.m., and I’m scrollin’ through the past...
[انقل باقي الكلمات هنا تماماً]
""".strip()

# 3. توليد صوت TTS (soft male English)
tts = gTTS(lyrics, lang='en', slow=False, tld='com')
tts.save("vocals_raw.mp3")
vocals = AudioSegment.from_file("vocals_raw.mp3")

# 4. محاذاة الطول مع البيت
vocals = vocals.set_duration(duration_ms)

# 5. مؤثر بسيط لمحاكاة Auto‑Tune
def apply_pitch_shift(data, semitones):
    factor = 2 ** (semitones / 12.0)
    resampled = sps.resample_poly(data, up=int(factor*100), down=100)
    return resampled

# تحويل AudioSegment إلى numpy array
samples = np.array(vocals.get_array_of_samples())
shifted = apply_pitch_shift(samples, semitones=0.5)  # shift نص Tone
shifted = shifted[:len(samples)]
# إعادة إلى AudioSegment
vocals_shifted = vocals._spawn(shifted.astype(np.int16).tobytes())

# 6. دمج vocals مع البيت
final = beat.overlay(vocals_shifted - 6)

# 7. تصدير النتيجة
final.export("still_in_the_silence_fulltrack.mp3", format="mp3")
print("✅ Finished: still_in_the_silence_fulltrack.mp3")